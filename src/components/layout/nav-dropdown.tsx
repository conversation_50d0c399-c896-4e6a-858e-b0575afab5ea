"use client"

import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import {
  ChevronDown,
  Heart,
  Users,
  BookOpen,
  Calendar,
  DollarSign,
  Info,
  Phone,
  CheckSquare,
  Dog,
  Cat,
  Shield,
  MapPin,
  ShoppingBag,
  FileText,
  Search,
  CreditCard,
  Tag,
  Pill,
  Activity,
  Brain,
  Apple,
  Stethoscope,
  AlertTriangle,
  Home
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

interface DropdownItem {
  title: string
  href: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  badge?: string
}

interface NavDropdownProps {
  title: string
  items: DropdownItem[]
  className?: string
}

export function NavDropdown({ title, items, className }: NavDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleMouseEnter = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      setTimeoutId(null)
    }
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    const id = setTimeout(() => {
      setIsOpen(false)
    }, 150) // Small delay to prevent flickering
    setTimeoutId(id)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault()
      setIsOpen(!isOpen)
    } else if (e.key === "Escape") {
      setIsOpen(false)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div
      ref={dropdownRef}
      className={cn("relative", className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        className={cn(
          "flex items-center space-x-1 text-sm font-medium transition-colors duration-200 py-2 px-1",
          "hover:text-primary focus:text-primary focus:outline-none",
          isOpen && "text-primary"
        )}
        onKeyDown={handleKeyDown}
        aria-expanded={isOpen}
        aria-haspopup="true"
        role="button"
        tabIndex={0}
      >
        <span>{title}</span>
        <ChevronDown 
          className={cn(
            "h-4 w-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )} 
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden"
            role="menu"
            aria-orientation="vertical"
          >
            <div className="py-2">
              {items.map((item, index) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "block px-4 py-3 hover:bg-gray-50 transition-colors duration-150",
                      "focus:bg-gray-50 focus:outline-none"
                    )}
                    role="menuitem"
                    tabIndex={-1}
                    onClick={() => setIsOpen(false)}
                  >
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-start space-x-3"
                    >
                      {Icon && (
                        <div className="flex-shrink-0 mt-1">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <div className="font-medium text-gray-900 truncate">
                            {item.title}
                          </div>
                          {item.badge && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        {item.description && (
                          <div className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </Link>
                )
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Predefined navigation items for common dropdowns
export const adoptionDropdownItems: DropdownItem[] = [
  {
    title: "Find Pets",
    href: "/pets",
    description: "Browse available pets for adoption",
    icon: Heart
  },
  {
    title: "Adoption Checklist",
    href: "/adoption-checklist",
    description: "Essential steps for pet adoption",
    icon: CheckSquare
  },
  {
    title: "Dog Breed Guide",
    href: "/dog-breeds",
    description: "Learn about different dog breeds",
    icon: Dog
  },
  {
    title: "Cat Breeds",
    href: "/cat-breeds",
    description: "Discover various cat breeds",
    icon: Cat
  },
  {
    title: "Adoption Process",
    href: "/adoption-process",
    description: "Learn about our adoption requirements",
    icon: BookOpen
  },
  {
    title: "Schedule Visit",
    href: "/schedule-visit",
    description: "Book a visit to meet your future pet",
    icon: Calendar
  }
]

export const getInvolvedDropdownItems: DropdownItem[] = [
  {
    title: "Volunteer",
    href: "/volunteer",
    description: "Join our volunteer community",
    icon: Users,
    badge: "Popular"
  },
  {
    title: "Foster",
    href: "/foster",
    description: "Provide temporary homes for pets",
    icon: Heart
  },
  {
    title: "Donate",
    href: "/donate",
    description: "Support our mission financially",
    icon: DollarSign
  },
  {
    title: "Events",
    href: "/events",
    description: "Attend adoption events and fundraisers",
    icon: Calendar
  }
]

export const caringForYourPetDropdownItems: DropdownItem[] = [
  {
    title: "Pet Names",
    href: "/pet-care/names",
    description: "Find the perfect name for your pet",
    icon: Heart
  },
  {
    title: "Medications",
    href: "/pet-care/medications",
    description: "Pet medication guides and safety",
    icon: Pill
  },
  {
    title: "Illnesses & Symptoms",
    href: "/pet-care/illnesses",
    description: "Common pet health issues",
    icon: Stethoscope
  },
  {
    title: "Training & Behavior",
    href: "/pet-care/training",
    description: "Training guides and behavior tips",
    icon: Brain
  },
  {
    title: "Diet & Nutrition",
    href: "/pet-care/diet",
    description: "Proper nutrition for your pet",
    icon: Apple
  },
  {
    title: "Treatment & Remedies",
    href: "/pet-care/treatment",
    description: "Safe home remedies and first aid",
    icon: Activity
  },
  {
    title: "Poison Prevention",
    href: "/pet-care/poison-prevention",
    description: "Keep your pet safe from toxins",
    icon: AlertTriangle
  },
  {
    title: "Safety",
    href: "/pet-care/safety",
    description: "Comprehensive pet safety guide",
    icon: Shield
  }
]

export const petInsuranceDropdownItems: DropdownItem[] = [
  {
    title: "Compare Pet Insurance Plans",
    href: "/pet-insurance/compare",
    description: "Find the best insurance for your pet",
    icon: CreditCard
  },
  {
    title: "Pet Insurance by State",
    href: "/pet-insurance/by-state",
    description: "State-specific insurance options",
    icon: MapPin
  },
  {
    title: "Pet Insurance Articles",
    href: "/pet-insurance/articles",
    description: "Learn about pet insurance benefits",
    icon: FileText
  }
]

export const lostPetProtectionDropdownItems: DropdownItem[] = [
  {
    title: "Memberships",
    href: "/lost-pet/memberships",
    description: "Join our lost pet protection program",
    icon: Shield
  },
  {
    title: "Report a Lost or Found Pet",
    href: "/lost-pet/report",
    description: "Report missing or found pets",
    icon: Search
  },
  {
    title: "ByteTag",
    href: "/lost-pet/bytetag",
    description: "Smart pet identification tags",
    icon: Tag
  },
  {
    title: "Microchipping",
    href: "/lost-pet/microchipping",
    description: "Permanent pet identification",
    icon: Activity
  },
  {
    title: "Register My Pet / Microchip",
    href: "/lost-pet/register",
    description: "Register your pet's information",
    icon: FileText
  },
  {
    title: "Lost Pet Resources",
    href: "/lost-pet/resources",
    description: "Tips for finding lost pets",
    icon: BookOpen
  }
]

export const shopDropdownItems: DropdownItem[] = [
  {
    title: "Pumpkin Wellness Club",
    href: "/shop/pumpkin-wellness",
    description: "Comprehensive pet wellness plans",
    icon: Heart
  },
  {
    title: "ByteTag",
    href: "/shop/bytetag",
    description: "Smart pet identification tags",
    icon: Tag
  },
  {
    title: "PawPack",
    href: "/shop/pawpack",
    description: "Monthly pet supply boxes",
    icon: ShoppingBag
  }
]

export const resourcesDropdownItems: DropdownItem[] = [
  {
    title: "Pet Care Guide",
    href: "/pet-care",
    description: "Essential tips for pet care",
    icon: BookOpen,
    badge: "New"
  },
  {
    title: "Blog",
    href: "/blog",
    description: "Latest news and stories",
    icon: BookOpen
  },
  {
    title: "Consumer Survey",
    href: "/consumer-survey",
    description: "Share your feedback with us",
    icon: FileText,
    badge: "2025"
  }
]

export const aboutDropdownItems: DropdownItem[] = [
  {
    title: "About Us",
    href: "/about",
    description: "Learn about our mission and story",
    icon: Info
  },
  {
    title: "Adoption Stories",
    href: "/adoption-stories",
    description: "Heartwarming tales of successful adoptions",
    icon: Heart,
    badge: "New"
  },
  {
    title: "Contact",
    href: "/contact",
    description: "Get in touch with our team",
    icon: Phone
  },
  {
    title: "FAQ",
    href: "/faq",
    description: "Frequently asked questions",
    icon: BookOpen
  },
  {
    title: "Privacy Policy",
    href: "/privacy",
    description: "Our privacy and data policies",
    icon: Info
  }
]
