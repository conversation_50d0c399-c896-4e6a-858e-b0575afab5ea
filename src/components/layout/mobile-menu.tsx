"use client"

import { useState } from "react"
import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import {
  X,
  ChevronDown,
  Heart,
  Users,
  BookOpen,
  Calendar,
  DollarSign,
  Info,
  Phone,
  Shield,
  Settings,
  User,
  LogOut,
  CheckSquare,
  Dog,
  Cat,
  MapPin,
  ShoppingBag,
  FileText,
  Search,
  CreditCard,
  Tag,
  Pill,
  Activity,
  Brain,
  Apple,
  Stethoscope,
  AlertTriangle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

import { SearchBar } from "./search-bar"

interface MobileMenuProps {
  isOpen: boolean
  onClose: () => void
}

interface MobileNavItem {
  title: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
  children?: MobileNavItem[]
  badge?: string
}

const navigationItems: MobileNavItem[] = [
  {
    title: "Adopt a Pet",
    icon: Heart,
    children: [
      { title: "Find Pets", href: "/pets", icon: Heart },
      { title: "Adoption Checklist", href: "/adoption-checklist", icon: CheckSquare },
      { title: "Dog Breed Guide", href: "/dog-breeds", icon: Dog },
      { title: "Cat Breeds", href: "/cat-breeds", icon: Cat },
      { title: "Adoption Process", href: "/adoption-process", icon: BookOpen },
      { title: "Schedule Visit", href: "/schedule-visit", icon: Calendar }
    ]
  },
  {
    title: "Caring for Your Pet",
    icon: Heart,
    children: [
      { title: "Pet Names", href: "/pet-care/names", icon: Heart },
      { title: "Medications", href: "/pet-care/medications", icon: Pill },
      { title: "Illnesses & Symptoms", href: "/pet-care/illnesses", icon: Stethoscope },
      { title: "Training & Behavior", href: "/pet-care/training", icon: Brain },
      { title: "Diet & Nutrition", href: "/pet-care/diet", icon: Apple },
      { title: "Treatment & Remedies", href: "/pet-care/treatment", icon: Activity },
      { title: "Poison Prevention", href: "/pet-care/poison-prevention", icon: AlertTriangle },
      { title: "Safety", href: "/pet-care/safety", icon: Shield }
    ]
  },
  {
    title: "Pet Insurance",
    icon: Shield,
    children: [
      { title: "Compare Pet Insurance Plans", href: "/pet-insurance/compare", icon: CreditCard },
      { title: "Pet Insurance by State", href: "/pet-insurance/by-state", icon: MapPin },
      { title: "Pet Insurance Articles", href: "/pet-insurance/articles", icon: FileText }
    ]
  },
  {
    title: "Lost Pet Protection",
    icon: Search,
    children: [
      { title: "Memberships", href: "/lost-pet/memberships", icon: Shield },
      { title: "Report a Lost or Found Pet", href: "/lost-pet/report", icon: Search },
      { title: "ByteTag", href: "/lost-pet/bytetag", icon: Tag },
      { title: "Microchipping", href: "/lost-pet/microchipping", icon: Activity },
      { title: "Register My Pet / Microchip", href: "/lost-pet/register", icon: FileText },
      { title: "Lost Pet Resources", href: "/lost-pet/resources", icon: BookOpen }
    ]
  },
  {
    title: "Shop",
    icon: ShoppingBag,
    children: [
      { title: "Pumpkin Wellness Club", href: "/shop/pumpkin-wellness", icon: Heart },
      { title: "ByteTag", href: "/shop/bytetag", icon: Tag },
      { title: "PawPack", href: "/shop/pawpack", icon: ShoppingBag }
    ]
  },
  {
    title: "Get Involved",
    icon: Users,
    children: [
      { title: "Events", href: "/events", icon: Calendar },
      { title: "Volunteer", href: "/volunteer", icon: Users, badge: "Popular" },
      { title: "Foster", href: "/foster", icon: Heart },
      { title: "Donate", href: "/donate", icon: DollarSign }
    ]
  },
  {
    title: "Resources",
    icon: BookOpen,
    children: [
      { title: "Pet Care Guide", href: "/pet-care", icon: BookOpen, badge: "New" },
      { title: "Blog", href: "/blog", icon: BookOpen },
      { title: "Consumer Survey", href: "/consumer-survey", icon: FileText, badge: "2025" }
    ]
  },
  {
    title: "About",
    icon: Info,
    children: [
      { title: "About Us", href: "/about", icon: Info },
      { title: "Contact", href: "/contact", icon: Phone },
      { title: "FAQ", href: "/faq", icon: BookOpen },
      { title: "Privacy Policy", href: "/privacy", icon: Info }
    ]
  }
]

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const { data: session } = useSession()
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  
  const handleKeyDown = (e: React.KeyboardEvent, title: string) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      toggleExpanded(title)
    }
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const hasAdminAccess = () => {
    return session?.user?.role && ['ADMIN', 'STAFF'].includes(session.user.role as string)
  }

  const isAdmin = () => {
    return session?.user?.role === 'ADMIN'
  }

  const handleLinkClick = () => {
    onClose()
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={onClose}
            role="presentation"
          >
          </motion.div>

          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{
              type: "spring",
              damping: 25,
              stiffness: 200,
            }}
            className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-xl z-50 md:hidden overflow-y-auto"
            role="dialog"
            aria-modal="true"
            aria-label="Mobile menu"
          >
            <div className="flex justify-between items-center h-16 px-4 border-b">
              <h2 className="text-lg font-semibold">Menu</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-8 w-8"
                aria-label="Close menu"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="mb-4">
              <SearchBar className="px-4 py-2" />
            </div>

            <div className="mb-4 space-y-2 px-4">
              <Link href="/lost-pet/report" onClick={onClose}>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white">
                  <Search className="w-4 h-4 mr-2" />
                  Report Lost Pet
                </Button>
              </Link>
              <Link href="/donate" onClick={onClose}>
                <Button className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white">
                  <Heart className="w-4 h-4 mr-2" />
                  Donate Now
                </Button>
              </Link>
            </div>

            <nav>
              {navigationItems.map((item) => (
                <div key={item.title}>
                  {item.children ? (
                    <div>
                      <button
                        onClick={() => toggleExpanded(item.title)}
                        onKeyDown={(e) => handleKeyDown(e, item.title)}
                        className="w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                        aria-expanded={expandedItems.includes(item.title)}
                        aria-controls={`${item.title}-menu`}
                      >
                        <div className="flex items-center space-x-3">
                          {item.icon && <item.icon className="h-5 w-5 text-gray-600" />}
                          <span>{item.title}</span>
                        </div>
                        <ChevronDown
                          className={cn(
                            "h-4 w-4 text-gray-400 transition-transform duration-200",
                            expandedItems.includes(item.title) && "rotate-180"
                          )}
                        />
                      </button>

                      <AnimatePresence>
                        {expandedItems.includes(item.title) && (
                          <motion.div
                            id={`${item.title}-menu`}
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden bg-gray-50"
                            role="region"
                            aria-labelledby={`${item.title}-heading`}
                          >
                            {item.children.map((child) => (
                              <Link
                                key={child.title}
                                href={child.href || "#"}
                                onClick={handleLinkClick}
                                className="flex items-center space-x-3 px-8 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                role="menuitem"
                              >
                                <div className="flex items-center space-x-3">
                                  {child.icon && <child.icon className="h-4 w-4 text-gray-500" />}
                                  <span>{child.title}</span>
                                </div>
                                {child.badge && (
                                  <span className="ml-auto text-xs font-medium text-primary px-2 py-1 bg-primary/10 rounded-full">
                                    {child.badge}
                                  </span>
                                )}
                              </Link>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      href={item.href || "#"}
                      onClick={handleLinkClick}
                      className="flex items-center space-x-3 px-4 py-3 hover:bg-gray-50 transition-colors"
                      role="menuitem"
                    >
                      {item.icon && <item.icon className="h-5 w-5 text-gray-600" />}
                      <span>{item.title}</span>
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            {/* Account section */}
            <div className="border-t mt-4 pt-4">
              {session ? (
                <div>
                  <div className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <div className="font-medium text-gray-900">
                        {session.user?.name || session.user?.email}
                      </div>
                    </div>
                  </div>

                  {/* Account links */}
                  <Link
                    href="/profile"
                    onClick={handleLinkClick}
                    className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    role="menuitem"
                  >
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </Link>

                  <Link
                    href="/profile/settings"
                    onClick={handleLinkClick}
                    className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    role="menuitem"
                  >
                    <Settings className="h-4 w-4" />
                    <span>Account Settings</span>
                  </Link>

                  {hasAdminAccess() && (
                    <div>
                      <Link
                        href="/admin"
                        onClick={handleLinkClick}
                        className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        role="menuitem"
                      >
                        <Shield className="h-4 w-4" />
                        <span>Admin Panel</span>
                      </Link>

                      {isAdmin() && (
                        <Link
                          href="/admin/users"
                          onClick={handleLinkClick}
                          className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          role="menuitem"
                        >
                          <Users className="h-4 w-4" />
                          <span>User Management</span>
                        </Link>
                      )}
                    </div>
                  )}

                  <button
                    onClick={() => {
                      signOut()
                      handleLinkClick()
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Sign Out</span>
                  </button>
                </div>
              ) : (
                <div className="p-4 space-y-2">
                  <Link href="/auth/signin" onClick={handleLinkClick}>
                    <Button variant="outline" className="w-full">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth/signup" onClick={handleLinkClick}>
                    <Button className="w-full">
                      Sign Up
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}
