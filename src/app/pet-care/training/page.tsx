"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Search, 
  Star, 
  Clock, 
  Heart,
  Target,
  Award,
  Brain,
  Zap,
  Shield,
  AlertCircle,
  CheckCircle,
  Play
} from "lucide-react"
import { motion } from "framer-motion"

interface TrainingTopic {
  title: string
  category: string
  difficulty: "beginner" | "intermediate" | "advanced"
  timeToLearn: string
  description: string
  steps: string[]
  tips: string[]
  commonMistakes: string[]
  species: "dogs" | "cats" | "both"
}

const trainingTopics: TrainingTopic[] = [
  {
    title: "Basic Sit Command",
    category: "basic-commands",
    difficulty: "beginner",
    timeToLearn: "1-2 weeks",
    description: "Teaching your dog to sit on command is one of the fundamental building blocks of training.",
    steps: [
      "Hold a treat close to your dog's nose",
      "Slowly move the treat up, allowing their head to follow the treat",
      "As their head moves up, their bottom should naturally lower",
      "Once sitting, say 'Sit' and give the treat immediately",
      "Repeat 5-10 times per session, 2-3 sessions daily"
    ],
    tips: [
      "Keep training sessions short (5-10 minutes)",
      "Use high-value treats your dog loves",
      "Practice in a quiet environment initially",
      "Be patient and consistent"
    ],
    commonMistakes: [
      "Saying the command too early",
      "Not rewarding immediately",
      "Training for too long",
      "Getting frustrated"
    ],
    species: "dogs"
  },
  {
    title: "Litter Box Training",
    category: "house-training",
    difficulty: "beginner",
    timeToLearn: "1-4 weeks",
    description: "Teaching your cat to consistently use the litter box for elimination.",
    steps: [
      "Choose the right litter box size and type",
      "Place box in a quiet, accessible location",
      "Fill with 2-3 inches of unscented litter",
      "Show your cat the box location",
      "Place cat in box after meals and naps",
      "Clean box daily and praise when used correctly"
    ],
    tips: [
      "Keep the box clean at all times",
      "Avoid scented litters initially",
      "Have one box per cat plus one extra",
      "Never punish accidents"
    ],
    commonMistakes: [
      "Box is too small or dirty",
      "Using scented litter",
      "Placing box in noisy area",
      "Punishing accidents"
    ],
    species: "cats"
  },
  {
    title: "Leash Walking",
    category: "walking",
    difficulty: "intermediate",
    timeToLearn: "2-6 weeks",
    description: "Training your dog to walk calmly on a leash without pulling.",
    steps: [
      "Start indoors with collar and leash",
      "Let dog get comfortable wearing equipment",
      "Practice walking indoors first",
      "Move to quiet outdoor areas",
      "Stop moving when dog pulls",
      "Reward when leash is loose",
      "Gradually increase distractions"
    ],
    tips: [
      "Use a properly fitted collar or harness",
      "Start with short 5-10 minute walks",
      "Bring high-value treats",
      "Be consistent with rules"
    ],
    commonMistakes: [
      "Allowing pulling to continue",
      "Using retractable leashes for training",
      "Walking in high-distraction areas too soon",
      "Inconsistent rules"
    ],
    species: "dogs"
  },
  {
    title: "Scratching Post Training",
    category: "behavior",
    difficulty: "beginner",
    timeToLearn: "2-4 weeks",
    description: "Teaching your cat to use appropriate scratching surfaces instead of furniture.",
    steps: [
      "Choose the right scratching post material",
      "Place posts near sleeping areas",
      "Make posts more attractive than furniture",
      "Use catnip or treats to encourage use",
      "Gently redirect when scratching furniture",
      "Reward when using post correctly"
    ],
    tips: [
      "Posts should be tall and sturdy",
      "Try different materials (sisal, carpet, cardboard)",
      "Place multiple posts around the house",
      "Never declaw as punishment"
    ],
    commonMistakes: [
      "Post is too short or unstable",
      "Only having one scratching option",
      "Punishing natural scratching behavior",
      "Not making posts attractive enough"
    ],
    species: "cats"
  }
]

const behaviorProblems = [
  {
    problem: "Excessive Barking",
    species: "dogs",
    causes: ["Boredom", "Anxiety", "Territorial behavior", "Attention seeking"],
    solutions: [
      "Identify and address the trigger",
      "Provide mental stimulation",
      "Teach 'quiet' command",
      "Ignore attention-seeking barking",
      "Consider professional training"
    ]
  },
  {
    problem: "Inappropriate Elimination",
    species: "cats",
    causes: ["Medical issues", "Dirty litter box", "Stress", "Litter preference"],
    solutions: [
      "Rule out medical problems first",
      "Keep litter boxes very clean",
      "Reduce stress factors",
      "Try different litter types",
      "Add more litter boxes"
    ]
  },
  {
    problem: "Destructive Chewing",
    species: "dogs",
    causes: ["Teething", "Boredom", "Anxiety", "Lack of exercise"],
    solutions: [
      "Provide appropriate chew toys",
      "Increase physical exercise",
      "Mental stimulation activities",
      "Puppy-proof the environment",
      "Redirect to appropriate items"
    ]
  }
]

export default function TrainingPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedDifficulty, setSelectedDifficulty] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("both")

  const filteredTopics = trainingTopics.filter(topic => {
    const matchesSearch = topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         topic.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || topic.category === selectedCategory
    const matchesDifficulty = selectedDifficulty === "all" || topic.difficulty === selectedDifficulty
    const matchesSpecies = selectedSpecies === "both" || topic.species === selectedSpecies || topic.species === "both"
    return matchesSearch && matchesCategory && matchesDifficulty && matchesSpecies
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner": return "bg-green-100 text-green-800"
      case "intermediate": return "bg-yellow-100 text-yellow-800"
      case "advanced": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const categories = Array.from(new Set(trainingTopics.map(topic => topic.category)))

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Training
              <span className="block text-green-300">& Behavior Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Build a stronger bond with your pet through positive training and behavior management
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search training topics..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category.replace("-", " ")}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="training" className="mb-8">
          <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
            <TabsTrigger value="training">Training Guides</TabsTrigger>
            <TabsTrigger value="behavior">Behavior Problems</TabsTrigger>
          </TabsList>

          <TabsContent value="training" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredTopics.map((topic, index) => (
                <motion.div
                  key={topic.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl">{topic.title}</CardTitle>
                          <CardDescription className="mt-2">{topic.description}</CardDescription>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge className={getDifficultyColor(topic.difficulty)}>
                            {topic.difficulty}
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {topic.species === "both" ? "Dogs & Cats" : topic.species}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {topic.timeToLearn}
                        </div>
                        <div className="flex items-center">
                          <Target className="w-4 h-4 mr-1" />
                          {topic.category.replace("-", " ")}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2 flex items-center">
                          <Play className="w-4 h-4 mr-1" />
                          Training Steps:
                        </h4>
                        <ol className="text-sm text-gray-600 space-y-1">
                          {topic.steps.map((step, idx) => (
                            <li key={idx} className="flex items-start">
                              <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                                {idx + 1}
                              </span>
                              {step}
                            </li>
                          ))}
                        </ol>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-green-700 mb-2 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Pro Tips:
                        </h4>
                        <ul className="text-sm text-green-600 space-y-1">
                          {topic.tips.map((tip, idx) => (
                            <li key={idx} className="flex items-start">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 mt-2"></div>
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm text-orange-700 mb-2 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          Avoid These Mistakes:
                        </h4>
                        <ul className="text-sm text-orange-600 space-y-1">
                          {topic.commonMistakes.map((mistake, idx) => (
                            <li key={idx} className="flex items-start">
                              <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2 mt-2"></div>
                              {mistake}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="behavior" className="mt-8">
            <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {behaviorProblems.map((problem, index) => (
                <motion.div
                  key={problem.problem}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">{problem.problem}</CardTitle>
                      <Badge variant="outline" className="w-fit capitalize">
                        {problem.species}
                      </Badge>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-red-700 mb-2">Common Causes:</h4>
                        <div className="flex flex-wrap gap-1">
                          {problem.causes.map((cause, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {cause}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-green-700 mb-2">Solutions:</h4>
                        <ul className="text-sm text-green-600 space-y-1">
                          {problem.solutions.map((solution, idx) => (
                            <li key={idx} className="flex items-start">
                              <CheckCircle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {solution}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Training Principles */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Positive Training Principles</CardTitle>
            <CardDescription className="text-center">
              Foundation principles for successful pet training
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  title: "Consistency",
                  description: "Use the same commands and rules every time",
                  icon: Target
                },
                {
                  title: "Patience",
                  description: "Learning takes time - stay calm and positive",
                  icon: Heart
                },
                {
                  title: "Rewards",
                  description: "Positive reinforcement works better than punishment",
                  icon: Award
                },
                {
                  title: "Short Sessions",
                  description: "Keep training sessions brief but frequent",
                  icon: Clock
                }
              ].map((principle, index) => (
                <motion.div
                  key={principle.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <principle.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{principle.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{principle.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
