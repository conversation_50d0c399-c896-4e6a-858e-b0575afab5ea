"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search, 
  AlertTriangle, 
  Clock, 
  Heart,
  Thermometer,
  Stethoscope,
  Eye,
  Ear,
  Activity,
  Phone,
  Zap,
  Shield
} from "lucide-react"
import { motion } from "framer-motion"

interface Illness {
  name: string
  category: string
  symptoms: string[]
  commonCauses: string[]
  urgency: "low" | "medium" | "high" | "emergency"
  description: string
  whenToSeeVet: string
  species: "dogs" | "cats" | "both"
  prevention?: string[]
}

const illnesses: Illness[] = [
  {
    name: "<PERSON><PERSON>ugh",
    category: "respiratory",
    symptoms: ["Dry, hacking cough", "Retching", "Gagging", "Runny nose", "Lethargy"],
    commonCauses: ["Viral infection", "Bacterial infection", "Stress", "Poor ventilation"],
    urgency: "medium",
    description: "A highly contagious respiratory infection that causes inflammation of the trachea and bronchi.",
    whenToSeeVet: "If cough persists more than a few days or if pet shows signs of pneumonia",
    species: "dogs",
    prevention: ["Vaccination", "Avoid crowded areas", "Good ventilation", "Reduce stress"]
  },
  {
    name: "Urinary Tract Infection",
    category: "urinary",
    symptoms: ["Frequent urination", "Straining to urinate", "Blood in urine", "Strong odor", "Accidents in house"],
    commonCauses: ["Bacteria", "Bladder stones", "Diabetes", "Poor hygiene"],
    urgency: "medium",
    description: "Bacterial infection of the urinary tract that can affect the bladder, urethra, or kidneys.",
    whenToSeeVet: "If symptoms persist more than 24 hours or if pet cannot urinate",
    species: "both",
    prevention: ["Fresh water access", "Regular bathroom breaks", "Proper hygiene", "Quality diet"]
  },
  {
    name: "Gastroenteritis",
    category: "digestive",
    symptoms: ["Vomiting", "Diarrhea", "Loss of appetite", "Dehydration", "Abdominal pain"],
    commonCauses: ["Dietary indiscretion", "Food allergies", "Parasites", "Stress", "Infections"],
    urgency: "medium",
    description: "Inflammation of the stomach and intestines causing digestive upset.",
    whenToSeeVet: "If symptoms last more than 24 hours or if severe dehydration occurs",
    species: "both",
    prevention: ["Consistent diet", "Avoid table scraps", "Regular deworming", "Stress management"]
  },
  {
    name: "Ear Infection",
    category: "ear",
    symptoms: ["Head shaking", "Scratching ears", "Odor from ears", "Dark discharge", "Redness"],
    commonCauses: ["Bacteria", "Yeast", "Allergies", "Moisture", "Foreign objects"],
    urgency: "medium",
    description: "Infection or inflammation of the ear canal, common in dogs with floppy ears.",
    whenToSeeVet: "If symptoms persist or worsen after 2-3 days",
    species: "both",
    prevention: ["Regular ear cleaning", "Keep ears dry", "Allergy management", "Proper grooming"]
  },
  {
    name: "Bloat (GDV)",
    category: "emergency",
    symptoms: ["Swollen abdomen", "Retching without vomiting", "Restlessness", "Drooling", "Rapid breathing"],
    commonCauses: ["Large meals", "Exercise after eating", "Genetics", "Stress"],
    urgency: "emergency",
    description: "Life-threatening condition where the stomach fills with gas and may twist.",
    whenToSeeVet: "IMMEDIATELY - This is a medical emergency",
    species: "dogs",
    prevention: ["Small frequent meals", "Avoid exercise after eating", "Slow feeding", "Stress reduction"]
  }
]

const urgencyLevels = [
  { level: "low", color: "bg-green-100 text-green-800", label: "Monitor at Home" },
  { level: "medium", color: "bg-yellow-100 text-yellow-800", label: "See Vet Soon" },
  { level: "high", color: "bg-orange-100 text-orange-800", label: "See Vet Today" },
  { level: "emergency", color: "bg-red-100 text-red-800", label: "Emergency!" }
]

const categories = [
  { id: "respiratory", name: "Respiratory", icon: Activity, color: "bg-blue-100 text-blue-800" },
  { id: "digestive", name: "Digestive", icon: Heart, color: "bg-green-100 text-green-800" },
  { id: "urinary", name: "Urinary", icon: Thermometer, color: "bg-purple-100 text-purple-800" },
  { id: "skin", name: "Skin", icon: Shield, color: "bg-pink-100 text-pink-800" },
  { id: "ear", name: "Ear", icon: Ear, color: "bg-indigo-100 text-indigo-800" },
  { id: "eye", name: "Eye", icon: Eye, color: "bg-cyan-100 text-cyan-800" },
  { id: "emergency", name: "Emergency", icon: AlertTriangle, color: "bg-red-100 text-red-800" }
]

const emergencySigns = [
  "Difficulty breathing or choking",
  "Unconsciousness or collapse",
  "Severe bleeding",
  "Suspected poisoning",
  "Seizures",
  "Inability to urinate or defecate",
  "Severe abdominal swelling",
  "High fever (over 104°F)",
  "Severe trauma or injury",
  "Pale or blue gums"
]

export default function IllnessesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedUrgency, setSelectedUrgency] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("both")

  const filteredIllnesses = illnesses.filter(illness => {
    const matchesSearch = illness.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         illness.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         illness.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || illness.category === selectedCategory
    const matchesUrgency = selectedUrgency === "all" || illness.urgency === selectedUrgency
    const matchesSpecies = selectedSpecies === "both" || illness.species === selectedSpecies || illness.species === "both"
    return matchesSearch && matchesCategory && matchesUrgency && matchesSpecies
  })

  const getUrgencyColor = (urgency: string) => {
    const level = urgencyLevels.find(l => l.level === urgency)
    return level?.color || "bg-gray-100 text-gray-800"
  }

  const getUrgencyLabel = (urgency: string) => {
    const level = urgencyLevels.find(l => l.level === urgency)
    return level?.label || urgency
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-red-600 to-blue-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Illnesses
              <span className="block text-red-300">& Symptoms Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Recognize symptoms early and know when to seek veterinary care for your pet
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Emergency Alert */}
        <Alert className="mb-8 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Emergency Warning</AlertTitle>
          <AlertDescription>
            If your pet shows any emergency signs, contact your veterinarian or emergency clinic immediately. 
            This guide is for educational purposes and does not replace professional veterinary care.
          </AlertDescription>
        </Alert>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search illnesses, symptoms, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedUrgency}
                  onChange={(e) => setSelectedUrgency(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Urgency</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="emergency">Emergency</option>
                </select>
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="illnesses" className="mb-8">
          <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
            <TabsTrigger value="illnesses">Common Illnesses</TabsTrigger>
            <TabsTrigger value="emergency">Emergency Signs</TabsTrigger>
          </TabsList>

          <TabsContent value="illnesses" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredIllnesses.map((illness, index) => (
                <motion.div
                  key={illness.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl">{illness.name}</CardTitle>
                          <CardDescription className="mt-2">{illness.description}</CardDescription>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge className={getUrgencyColor(illness.urgency)}>
                            {getUrgencyLabel(illness.urgency)}
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {illness.species === "both" ? "Dogs & Cats" : illness.species}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Symptoms to Watch For:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {illness.symptoms.map((symptom, idx) => (
                            <li key={idx} className="flex items-center">
                              <div className="w-1.5 h-1.5 bg-red-500 rounded-full mr-2"></div>
                              {symptom}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Common Causes:</h4>
                        <div className="flex flex-wrap gap-1">
                          {illness.commonCauses.map((cause, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {cause}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h4 className="font-semibold text-sm text-blue-700 mb-1">When to See a Vet:</h4>
                        <p className="text-sm text-blue-600">{illness.whenToSeeVet}</p>
                      </div>

                      {illness.prevention && (
                        <div>
                          <h4 className="font-semibold text-sm text-green-700 mb-2">Prevention Tips:</h4>
                          <ul className="text-sm text-green-600 space-y-1">
                            {illness.prevention.map((tip, idx) => (
                              <li key={idx} className="flex items-center">
                                <Shield className="w-3 h-3 mr-2 flex-shrink-0" />
                                {tip}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="emergency" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-red-600 flex items-center">
                  <AlertTriangle className="w-6 h-6 mr-2" />
                  Emergency Signs - Call Your Vet Immediately
                </CardTitle>
                <CardDescription>
                  If your pet shows any of these signs, seek emergency veterinary care immediately.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {emergencySigns.map((sign, index) => (
                    <motion.div
                      key={sign}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg"
                    >
                      <AlertTriangle className="w-5 h-5 text-red-600 mr-3 flex-shrink-0" />
                      <span className="text-red-800 font-medium">{sign}</span>
                    </motion.div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-red-100 border border-red-300 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Phone className="w-5 h-5 text-red-600 mr-2" />
                    <h3 className="font-semibold text-red-800">Emergency Contacts</h3>
                  </div>
                  <p className="text-red-700 text-sm">
                    Keep your veterinarian's emergency number and the nearest 24-hour animal hospital 
                    contact information easily accessible. In case of poisoning, also have the 
                    Pet Poison Helpline number: (*************
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
