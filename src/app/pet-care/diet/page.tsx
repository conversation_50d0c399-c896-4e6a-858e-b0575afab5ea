"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search, 
  Clock, 
  Heart,
  Scale,
  Utensils,
  AlertTriangle,
  CheckCircle,
  X,
  Apple,
  Fish,
  Beef,
  Wheat
} from "lucide-react"
import { motion } from "framer-motion"

interface NutritionGuide {
  ageGroup: string
  species: "dogs" | "cats" | "both"
  dailyFeedings: number
  portionGuidance: string
  keyNutrients: string[]
  foodType: string
  specialConsiderations: string[]
}

const nutritionGuides: NutritionGuide[] = [
  {
    ageGroup: "Puppy (2-12 months)",
    species: "dogs",
    dailyFeedings: 3,
    portionGuidance: "1/2 to 2 cups per day (depending on breed size)",
    keyNutrients: ["High protein (22-32%)", "DHA for brain development", "Calcium for bones", "Calories: 50-60 per lb"],
    foodType: "Puppy-specific formula",
    specialConsiderations: ["Feed more frequently", "Transition slowly from mother's milk", "Monitor growth rate"]
  },
  {
    ageGroup: "Adult Dog (1-7 years)",
    species: "dogs",
    dailyFeedings: 2,
    portionGuidance: "1-3 cups per day (based on size and activity)",
    keyNutrients: ["Protein (18-25%)", "Balanced fats", "Fiber for digestion", "Calories: 25-30 per lb"],
    foodType: "Adult maintenance formula",
    specialConsiderations: ["Adjust for activity level", "Monitor weight", "Consider breed-specific needs"]
  },
  {
    ageGroup: "Senior Dog (7+ years)",
    species: "dogs",
    dailyFeedings: 2,
    portionGuidance: "Reduce portions by 10-20% if less active",
    keyNutrients: ["Moderate protein", "Joint support (glucosamine)", "Antioxidants", "Lower calories"],
    foodType: "Senior formula",
    specialConsiderations: ["Easier to digest", "May need softer food", "Regular vet checkups"]
  },
  {
    ageGroup: "Kitten (2-12 months)",
    species: "cats",
    dailyFeedings: 4,
    portionGuidance: "1/4 to 1 cup per day",
    keyNutrients: ["High protein (30-40%)", "Taurine", "DHA", "High calories for growth"],
    foodType: "Kitten formula",
    specialConsiderations: ["Free feeding often recommended", "Transition from mother's milk", "Rapid growth period"]
  },
  {
    ageGroup: "Adult Cat (1-7 years)",
    species: "cats",
    dailyFeedings: 2,
    portionGuidance: "1/2 to 1 cup per day",
    keyNutrients: ["High protein (26%+)", "Taurine", "Arachidonic acid", "Moderate fat"],
    foodType: "Adult cat formula",
    specialConsiderations: ["Obligate carnivore", "Fresh water essential", "Indoor vs outdoor needs"]
  },
  {
    ageGroup: "Senior Cat (7+ years)",
    species: "cats",
    dailyFeedings: 2,
    portionGuidance: "Adjust based on activity and health",
    keyNutrients: ["High-quality protein", "Joint support", "Kidney support", "Antioxidants"],
    foodType: "Senior cat formula",
    specialConsiderations: ["Monitor kidney function", "May need prescription diet", "Easier to digest"]
  }
]

const safeHumanFoods = [
  { name: "Carrots", species: "dogs", benefits: "Beta-carotene, fiber, low calorie" },
  { name: "Blueberries", species: "both", benefits: "Antioxidants, vitamins, low calorie" },
  { name: "Sweet Potato", species: "dogs", benefits: "Vitamin A, fiber, potassium" },
  { name: "Cooked Chicken", species: "both", benefits: "Lean protein, easy to digest" },
  { name: "Plain Rice", species: "both", benefits: "Easy on stomach, energy source" },
  { name: "Pumpkin", species: "both", benefits: "Fiber, aids digestion" },
  { name: "Green Beans", species: "dogs", benefits: "Low calorie, vitamins, fiber" },
  { name: "Salmon", species: "both", benefits: "Omega-3 fatty acids, protein" }
]

const dangerousFoods = [
  { name: "Chocolate", species: "both", danger: "Toxic - can cause seizures and death" },
  { name: "Grapes/Raisins", species: "both", danger: "Kidney failure" },
  { name: "Onions/Garlic", species: "both", danger: "Destroys red blood cells" },
  { name: "Xylitol", species: "both", danger: "Severe hypoglycemia" },
  { name: "Avocado", species: "both", danger: "Persin toxicity" },
  { name: "Macadamia Nuts", species: "dogs", danger: "Weakness, vomiting, hyperthermia" },
  { name: "Raw Dough", species: "both", danger: "Alcohol poisoning, bloating" },
  { name: "Cooked Bones", species: "both", danger: "Splintering, choking hazard" }
]

const feedingTips = [
  {
    title: "Consistent Schedule",
    description: "Feed at the same times daily to establish routine",
    icon: Clock
  },
  {
    title: "Portion Control",
    description: "Measure food to prevent overfeeding and obesity",
    icon: Scale
  },
  {
    title: "Fresh Water",
    description: "Always provide clean, fresh water",
    icon: Heart
  },
  {
    title: "Gradual Changes",
    description: "Transition foods slowly over 7-10 days",
    icon: CheckCircle
  }
]

export default function DietPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSpecies, setSelectedSpecies] = useState("both")
  const [selectedAge, setSelectedAge] = useState("all")

  const filteredGuides = nutritionGuides.filter(guide => {
    const matchesSearch = guide.ageGroup.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guide.keyNutrients.some(nutrient => nutrient.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesSpecies = selectedSpecies === "both" || guide.species === selectedSpecies || guide.species === "both"
    const matchesAge = selectedAge === "all" || guide.ageGroup.toLowerCase().includes(selectedAge.toLowerCase())
    return matchesSearch && matchesSpecies && matchesAge
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Diet
              <span className="block text-orange-300">& Nutrition Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Proper nutrition is the foundation of your pet's health and happiness
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search nutrition guides..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedAge}
                  onChange={(e) => setSelectedAge(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Ages</option>
                  <option value="puppy">Puppy/Kitten</option>
                  <option value="adult">Adult</option>
                  <option value="senior">Senior</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="nutrition" className="mb-8">
          <TabsList className="grid w-full grid-cols-3 max-w-lg mx-auto">
            <TabsTrigger value="nutrition">Nutrition Guides</TabsTrigger>
            <TabsTrigger value="safe-foods">Safe Foods</TabsTrigger>
            <TabsTrigger value="dangerous-foods">Dangerous Foods</TabsTrigger>
          </TabsList>

          <TabsContent value="nutrition" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredGuides.map((guide, index) => (
                <motion.div
                  key={guide.ageGroup}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl">{guide.ageGroup}</CardTitle>
                          <CardDescription>Nutritional requirements and feeding guidelines</CardDescription>
                        </div>
                        <Badge variant="outline" className="capitalize">
                          {guide.species}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold text-sm text-gray-700 mb-1">Daily Feedings:</h4>
                          <p className="text-sm text-gray-600 flex items-center">
                            <Utensils className="w-4 h-4 mr-1" />
                            {guide.dailyFeedings} times
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-sm text-gray-700 mb-1">Food Type:</h4>
                          <p className="text-sm text-gray-600">{guide.foodType}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Portion Guidance:</h4>
                        <p className="text-sm text-gray-600 bg-blue-50 p-2 rounded">
                          {guide.portionGuidance}
                        </p>
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Key Nutrients:</h4>
                        <div className="flex flex-wrap gap-1">
                          {guide.keyNutrients.map((nutrient, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {nutrient}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm text-green-700 mb-2">Special Considerations:</h4>
                        <ul className="text-sm text-green-600 space-y-1">
                          {guide.specialConsiderations.map((consideration, idx) => (
                            <li key={idx} className="flex items-start">
                              <CheckCircle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {consideration}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="safe-foods" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-green-600 flex items-center">
                  <CheckCircle className="w-6 h-6 mr-2" />
                  Safe Human Foods for Pets
                </CardTitle>
                <CardDescription>
                  These foods can be given as occasional treats in moderation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {safeHumanFoods.map((food, index) => (
                    <motion.div
                      key={food.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="p-4 bg-green-50 border border-green-200 rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-green-800">{food.name}</h3>
                        <Badge className="bg-green-100 text-green-800 capitalize">
                          {food.species === "both" ? "Dogs & Cats" : food.species}
                        </Badge>
                      </div>
                      <p className="text-sm text-green-600">{food.benefits}</p>
                    </motion.div>
                  ))}
                </div>
                <Alert className="mt-6 border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Remember</AlertTitle>
                  <AlertDescription>
                    Even safe foods should only be given as treats and should not exceed 10% of your pet's daily caloric intake.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="dangerous-foods" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-red-600 flex items-center">
                  <X className="w-6 h-6 mr-2" />
                  Dangerous Foods - Never Give to Pets
                </CardTitle>
                <CardDescription>
                  These foods are toxic and can be life-threatening to pets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {dangerousFoods.map((food, index) => (
                    <motion.div
                      key={food.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="p-4 bg-red-50 border border-red-200 rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-red-800 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          {food.name}
                        </h3>
                        <Badge className="bg-red-100 text-red-800 capitalize">
                          {food.species === "both" ? "Dogs & Cats" : food.species}
                        </Badge>
                      </div>
                      <p className="text-sm text-red-600">{food.danger}</p>
                    </motion.div>
                  ))}
                </div>
                <Alert className="mt-6 border-red-200 bg-red-50">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Emergency Action</AlertTitle>
                  <AlertDescription>
                    If your pet consumes any of these foods, contact your veterinarian or pet poison control immediately. 
                    Do not wait for symptoms to appear.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Feeding Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">Essential Feeding Tips</CardTitle>
            <CardDescription className="text-center">
              Best practices for healthy feeding habits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {feedingTips.map((tip, index) => (
                <motion.div
                  key={tip.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="bg-orange-100 dark:bg-orange-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <tip.icon className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{tip.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{tip.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
