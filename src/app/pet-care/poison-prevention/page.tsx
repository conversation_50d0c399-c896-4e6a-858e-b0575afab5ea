"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search, 
  AlertTriangle, 
  Skull,
  Home,
  Flower,
  Pill,
  Trash,
  Phone,
  Clock,
  Shield,
  X,
  CheckCircle
} from "lucide-react"
import { motion } from "framer-motion"

interface ToxicItem {
  name: string
  category: string
  toxicityLevel: "mild" | "moderate" | "severe" | "fatal"
  symptoms: string[]
  timeToSymptoms: string
  species: "dogs" | "cats" | "both"
  description: string
  commonLocations: string[]
}

const toxicItems: ToxicItem[] = [
  {
    name: "Chocolate",
    category: "food",
    toxicityLevel: "severe",
    symptoms: ["Vomiting", "Diarrhea", "Increased heart rate", "Seizures", "Tremors"],
    timeToSymptoms: "1-4 hours",
    species: "both",
    description: "Contains theobromine which pets cannot metabolize effectively. Dark chocolate is more dangerous than milk chocolate.",
    commonLocations: ["Kitchen counters", "Pantry", "Gift boxes", "Halloween candy"]
  },
  {
    name: "Xylitol (Sugar-free gum)",
    category: "food",
    toxicityLevel: "fatal",
    symptoms: ["Rapid drop in blood sugar", "Vomiting", "Loss of coordination", "Collapse", "Liver failure"],
    timeToSymptoms: "10-60 minutes",
    species: "dogs",
    description: "Artificial sweetener that causes rapid insulin release in dogs, leading to severe hypoglycemia.",
    commonLocations: ["Purses", "Car glove compartments", "Kitchen drawers", "Sugar-free products"]
  },
  {
    name: "Lilies",
    category: "plants",
    toxicityLevel: "fatal",
    symptoms: ["Vomiting", "Lethargy", "Loss of appetite", "Kidney failure"],
    timeToSymptoms: "2-6 hours",
    species: "cats",
    description: "All parts of lily plants are extremely toxic to cats, causing rapid kidney failure.",
    commonLocations: ["Flower arrangements", "Gardens", "Indoor plants", "Gift bouquets"]
  },
  {
    name: "Grapes/Raisins",
    category: "food",
    toxicityLevel: "severe",
    symptoms: ["Vomiting", "Diarrhea", "Lethargy", "Kidney failure"],
    timeToSymptoms: "6-12 hours",
    species: "both",
    description: "Can cause acute kidney failure. The toxic compound is unknown, making any amount dangerous.",
    commonLocations: ["Fruit bowls", "Snack areas", "Baking ingredients", "Trail mix"]
  },
  {
    name: "Antifreeze",
    category: "chemicals",
    toxicityLevel: "fatal",
    symptoms: ["Sweet taste attracts pets", "Vomiting", "Seizures", "Kidney failure", "Death"],
    timeToSymptoms: "30 minutes - 12 hours",
    species: "both",
    description: "Ethylene glycol has a sweet taste but is extremely toxic. Even small amounts can be fatal.",
    commonLocations: ["Garages", "Driveways", "Car maintenance areas", "Storage sheds"]
  },
  {
    name: "Ibuprofen/Aspirin",
    category: "medications",
    toxicityLevel: "severe",
    symptoms: ["Stomach ulcers", "Kidney damage", "Vomiting", "Black stools", "Lethargy"],
    timeToSymptoms: "2-6 hours",
    species: "both",
    description: "Human pain medications are toxic to pets and can cause severe organ damage.",
    commonLocations: ["Medicine cabinets", "Purses", "Nightstands", "Kitchen counters"]
  }
]

const preventionTips = [
  {
    area: "Kitchen Safety",
    tips: [
      "Store chocolate and sweets in pet-proof containers",
      "Keep trash cans with tight-fitting lids",
      "Never leave food unattended on counters",
      "Clean up spills immediately",
      "Store cleaning products in locked cabinets"
    ],
    icon: Home
  },
  {
    area: "Medication Safety",
    tips: [
      "Store all medications in closed cabinets",
      "Never give human medications to pets",
      "Keep pill organizers out of reach",
      "Dispose of expired medications properly",
      "Be careful when dropping pills"
    ],
    icon: Pill
  },
  {
    area: "Plant Safety",
    tips: [
      "Research plants before bringing them home",
      "Keep toxic plants completely out of reach",
      "Consider artificial plants as alternatives",
      "Train pets to avoid plant areas",
      "Remove fallen leaves and flowers promptly"
    ],
    icon: Flower
  },
  {
    area: "Chemical Safety",
    tips: [
      "Store all chemicals in original containers",
      "Keep garage and basement areas pet-free",
      "Clean up antifreeze spills immediately",
      "Use pet-safe alternatives when possible",
      "Lock storage areas containing chemicals"
    ],
    icon: Shield
  }
]

const emergencySteps = [
  "Remove your pet from the source of poison",
  "Do NOT induce vomiting unless specifically instructed",
  "Call your veterinarian or Pet Poison Helpline immediately",
  "Collect the poison container/packaging if safe to do so",
  "Note the time of exposure and amount consumed",
  "Follow professional instructions exactly",
  "Transport to emergency clinic if advised"
]

export default function PoisonPreventionPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedToxicity, setSelectedToxicity] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("both")

  const filteredItems = toxicItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || item.category === selectedCategory
    const matchesToxicity = selectedToxicity === "all" || item.toxicityLevel === selectedToxicity
    const matchesSpecies = selectedSpecies === "both" || item.species === selectedSpecies || item.species === "both"
    return matchesSearch && matchesCategory && matchesToxicity && matchesSpecies
  })

  const getToxicityColor = (level: string) => {
    switch (level) {
      case "mild": return "bg-yellow-100 text-yellow-800"
      case "moderate": return "bg-orange-100 text-orange-800"
      case "severe": return "bg-red-100 text-red-800"
      case "fatal": return "bg-red-200 text-red-900"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const categories = Array.from(new Set(toxicItems.map(item => item.category)))

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-red-600 to-orange-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Poison Prevention
              <span className="block text-red-300">for Pets</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-orange-100">
              Protect your pets by knowing what's dangerous and how to prevent poisoning
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Emergency Alert */}
        <Alert className="mb-8 border-red-200 bg-red-50">
          <Phone className="h-4 w-4" />
          <AlertTitle>Emergency Poison Helpline</AlertTitle>
          <AlertDescription>
            <strong>Pet Poison Helpline: (*************</strong> - Available 24/7 for poison emergencies. 
            Also contact your veterinarian or nearest emergency animal hospital immediately.
          </AlertDescription>
        </Alert>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search toxic substances..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedToxicity}
                  onChange={(e) => setSelectedToxicity(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Toxicity Levels</option>
                  <option value="mild">Mild</option>
                  <option value="moderate">Moderate</option>
                  <option value="severe">Severe</option>
                  <option value="fatal">Fatal</option>
                </select>
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="toxic-items" className="mb-8">
          <TabsList className="grid w-full grid-cols-3 max-w-lg mx-auto">
            <TabsTrigger value="toxic-items">Toxic Items</TabsTrigger>
            <TabsTrigger value="prevention">Prevention Tips</TabsTrigger>
            <TabsTrigger value="emergency">Emergency Action</TabsTrigger>
          </TabsList>

          <TabsContent value="toxic-items" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow border-l-4 border-l-red-500">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl flex items-center">
                            <Skull className="w-5 h-5 mr-2 text-red-600" />
                            {item.name}
                          </CardTitle>
                          <CardDescription className="mt-2">{item.description}</CardDescription>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge className={getToxicityColor(item.toxicityLevel)}>
                            {item.toxicityLevel}
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {item.species === "both" ? "Dogs & Cats" : item.species}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-red-700 mb-2">Symptoms to Watch For:</h4>
                        <ul className="text-sm text-red-600 space-y-1">
                          {item.symptoms.map((symptom, idx) => (
                            <li key={idx} className="flex items-center">
                              <AlertTriangle className="w-3 h-3 mr-2 flex-shrink-0" />
                              {symptom}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold text-sm text-gray-700 mb-1">Time to Symptoms:</h4>
                          <p className="text-sm text-gray-600 flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {item.timeToSymptoms}
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-sm text-gray-700 mb-1">Category:</h4>
                          <Badge variant="outline" className="capitalize">
                            {item.category}
                          </Badge>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-sm text-orange-700 mb-2">Common Locations:</h4>
                        <div className="flex flex-wrap gap-1">
                          {item.commonLocations.map((location, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs bg-orange-50">
                              {location}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="prevention" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {preventionTips.map((area, index) => (
                <motion.div
                  key={area.area}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="text-xl flex items-center">
                        <area.icon className="w-6 h-6 mr-2 text-green-600" />
                        {area.area}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {area.tips.map((tip, idx) => (
                          <li key={idx} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="emergency" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-red-600 flex items-center">
                  <AlertTriangle className="w-6 h-6 mr-2" />
                  Emergency Action Plan
                </CardTitle>
                <CardDescription>
                  Follow these steps immediately if you suspect your pet has been poisoned
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Immediate Steps:</h3>
                    <ol className="space-y-3">
                      {emergencySteps.map((step, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="bg-red-500 text-white text-sm rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                            {idx + 1}
                          </span>
                          <span className="text-gray-700">{step}</span>
                        </li>
                      ))}
                    </ol>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                      <h4 className="font-semibold text-red-700 mb-2 flex items-center">
                        <X className="w-4 h-4 mr-1" />
                        DO NOT:
                      </h4>
                      <ul className="text-sm text-red-600 space-y-1">
                        <li>• Induce vomiting unless instructed</li>
                        <li>• Give milk or food</li>
                        <li>• Wait for symptoms to appear</li>
                        <li>• Try home remedies</li>
                      </ul>
                    </div>
                    
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <h4 className="font-semibold text-green-700 mb-2 flex items-center">
                        <Phone className="w-4 h-4 mr-1" />
                        Emergency Contacts:
                      </h4>
                      <div className="text-sm text-green-600 space-y-1">
                        <p><strong>Pet Poison Helpline:</strong> (*************</p>
                        <p><strong>Your Veterinarian:</strong> [Keep number handy]</p>
                        <p><strong>Emergency Clinic:</strong> [Keep number handy]</p>
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-700 mb-2">Information to Provide:</h4>
                      <ul className="text-sm text-blue-600 space-y-1">
                        <li>• Pet's weight and species</li>
                        <li>• What was consumed</li>
                        <li>• How much was consumed</li>
                        <li>• When it happened</li>
                        <li>• Current symptoms</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
