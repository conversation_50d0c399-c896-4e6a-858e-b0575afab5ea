"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search, 
  AlertTriangle, 
  Heart,
  Thermometer,
  Bandage,
  Scissors,
  Phone,
  Clock,
  CheckCircle,
  X,
  Shield,
  Stethoscope
} from "lucide-react"
import { motion } from "framer-motion"

interface HomeRemedy {
  condition: string
  remedy: string
  instructions: string[]
  ingredients: string[]
  safety: string
  whenToUseVet: string
  species: "dogs" | "cats" | "both"
  severity: "mild" | "moderate"
}

const homeRemedies: HomeRemedy[] = [
  {
    condition: "Minor Cuts & Scrapes",
    remedy: "Saline Solution Cleaning",
    instructions: [
      "Mix 1 tsp salt in 1 cup warm water",
      "Gently clean the wound with the solution",
      "Pat dry with clean cloth",
      "Apply thin layer of antibiotic ointment",
      "Monitor for signs of infection"
    ],
    ingredients: ["Salt", "Warm water", "Clean cloth", "Pet-safe antibiotic ointment"],
    safety: "Only for minor surface wounds. Do not use on deep cuts.",
    whenToUseVet: "If wound is deep, won't stop bleeding, or shows signs of infection",
    species: "both",
    severity: "mild"
  },
  {
    condition: "Upset Stomach",
    remedy: "Bland Diet",
    instructions: [
      "Fast for 12-24 hours (with vet approval)",
      "Offer small amounts of water frequently",
      "Start with plain boiled rice",
      "Add small amounts of boiled chicken (no skin/bones)",
      "Feed small portions every 2-3 hours",
      "Gradually return to normal diet over 3-5 days"
    ],
    ingredients: ["Plain white rice", "Boiled chicken breast", "Water"],
    safety: "Ensure pet stays hydrated. Not suitable for diabetic pets.",
    whenToUseVet: "If vomiting persists over 24 hours or signs of dehydration appear",
    species: "both",
    severity: "mild"
  },
  {
    condition: "Dry Skin",
    remedy: "Oatmeal Bath",
    instructions: [
      "Grind 1 cup oatmeal into fine powder",
      "Mix with warm (not hot) water",
      "Soak pet for 10-15 minutes",
      "Rinse thoroughly with clean water",
      "Pat dry gently with towel"
    ],
    ingredients: ["Plain oatmeal", "Warm water"],
    safety: "Use only plain, unflavored oatmeal. Test water temperature.",
    whenToUseVet: "If skin condition worsens or doesn't improve in a week",
    species: "both",
    severity: "mild"
  },
  {
    condition: "Ear Cleaning",
    remedy: "Gentle Ear Cleaning",
    instructions: [
      "Use pet-specific ear cleaner",
      "Apply cleaner to cotton ball",
      "Gently wipe visible parts of ear",
      "Never insert anything into ear canal",
      "Reward pet with treats"
    ],
    ingredients: ["Pet ear cleaner", "Cotton balls"],
    safety: "Never use cotton swabs or go deep into ear canal.",
    whenToUseVet: "If ears smell bad, are red, or pet shows pain",
    species: "both",
    severity: "mild"
  }
]

const firstAidSupplies = [
  { item: "Digital Thermometer", use: "Check for fever (normal: 101-102.5°F)" },
  { item: "Gauze Pads", use: "Cover wounds and control bleeding" },
  { item: "Medical Tape", use: "Secure bandages" },
  { item: "Antiseptic Wipes", use: "Clean minor wounds" },
  { item: "Saline Solution", use: "Flush wounds and eyes" },
  { item: "Tweezers", use: "Remove splinters or foreign objects" },
  { item: "Scissors", use: "Cut tape and gauze" },
  { item: "Emergency Vet Numbers", use: "Quick access to professional help" },
  { item: "Pet Carrier", use: "Safe transport to vet" },
  { item: "Muzzle", use: "Prevent biting when pet is in pain" }
]

const emergencyProcedures = [
  {
    emergency: "Choking",
    steps: [
      "Open mouth and look for visible object",
      "If visible, try to remove with tweezers",
      "For small dogs/cats: Hold upside down and give 5 back blows",
      "For large dogs: Lift hind legs, give 5 back blows",
      "Check mouth again",
      "Rush to emergency vet immediately"
    ],
    warning: "Do not use finger to sweep mouth blindly"
  },
  {
    emergency: "Bleeding",
    steps: [
      "Apply direct pressure with clean cloth",
      "Elevate wound above heart if possible",
      "Do not remove cloth if blood soaks through",
      "Add more layers on top",
      "Apply pressure bandage",
      "Seek immediate veterinary care"
    ],
    warning: "Severe bleeding requires immediate professional care"
  },
  {
    emergency: "Poisoning",
    steps: [
      "Remove pet from source of poison",
      "Do NOT induce vomiting unless instructed",
      "Call Pet Poison Helpline: (*************",
      "Collect sample of poison if safe to do so",
      "Follow poison control instructions",
      "Transport to emergency vet"
    ],
    warning: "Never induce vomiting with corrosive substances"
  }
]

export default function TreatmentPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSpecies, setSelectedSpecies] = useState("both")
  const [selectedSeverity, setSelectedSeverity] = useState("all")

  const filteredRemedies = homeRemedies.filter(remedy => {
    const matchesSearch = remedy.condition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         remedy.remedy.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSpecies = selectedSpecies === "both" || remedy.species === selectedSpecies || remedy.species === "both"
    const matchesSeverity = selectedSeverity === "all" || remedy.severity === selectedSeverity
    return matchesSearch && matchesSpecies && matchesSeverity
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Treatment & Home Remedies
              <span className="block text-purple-300">for Pets</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Safe home remedies and first aid knowledge for common pet health issues
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Important Warning */}
        <Alert className="mb-8 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Important Medical Disclaimer</AlertTitle>
          <AlertDescription>
            Home remedies are for minor conditions only. Always consult with a veterinarian for proper diagnosis 
            and treatment. In emergencies, seek immediate professional veterinary care.
          </AlertDescription>
        </Alert>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search conditions or remedies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedSeverity}
                  onChange={(e) => setSelectedSeverity(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Severities</option>
                  <option value="mild">Mild Only</option>
                  <option value="moderate">Moderate Only</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="remedies" className="mb-8">
          <TabsList className="grid w-full grid-cols-3 max-w-lg mx-auto">
            <TabsTrigger value="remedies">Home Remedies</TabsTrigger>
            <TabsTrigger value="first-aid">First Aid Kit</TabsTrigger>
            <TabsTrigger value="emergency">Emergency Care</TabsTrigger>
          </TabsList>

          <TabsContent value="remedies" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredRemedies.map((remedy, index) => (
                <motion.div
                  key={remedy.condition}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl">{remedy.condition}</CardTitle>
                          <CardDescription className="mt-1">{remedy.remedy}</CardDescription>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge className={remedy.severity === "mild" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}>
                            {remedy.severity}
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {remedy.species === "both" ? "Dogs & Cats" : remedy.species}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Ingredients Needed:</h4>
                        <div className="flex flex-wrap gap-1">
                          {remedy.ingredients.map((ingredient, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {ingredient}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-gray-700 mb-2">Instructions:</h4>
                        <ol className="text-sm text-gray-600 space-y-1">
                          {remedy.instructions.map((step, idx) => (
                            <li key={idx} className="flex items-start">
                              <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                                {idx + 1}
                              </span>
                              {step}
                            </li>
                          ))}
                        </ol>
                      </div>

                      <div className="bg-yellow-50 p-3 rounded-lg">
                        <h4 className="font-semibold text-sm text-yellow-700 mb-1 flex items-center">
                          <Shield className="w-4 h-4 mr-1" />
                          Safety Note:
                        </h4>
                        <p className="text-sm text-yellow-600">{remedy.safety}</p>
                      </div>

                      <div className="bg-red-50 p-3 rounded-lg">
                        <h4 className="font-semibold text-sm text-red-700 mb-1 flex items-center">
                          <Phone className="w-4 h-4 mr-1" />
                          When to See a Vet:
                        </h4>
                        <p className="text-sm text-red-600">{remedy.whenToUseVet}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="first-aid" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-center">Pet First Aid Kit Essentials</CardTitle>
                <CardDescription className="text-center">
                  Keep these items readily available for pet emergencies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {firstAidSupplies.map((supply, index) => (
                    <motion.div
                      key={supply.item}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-start p-4 bg-blue-50 border border-blue-200 rounded-lg"
                    >
                      <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-blue-800">{supply.item}</h3>
                        <p className="text-sm text-blue-600">{supply.use}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
                <Alert className="mt-6 border-blue-200 bg-blue-50">
                  <Stethoscope className="h-4 w-4" />
                  <AlertTitle>Pro Tip</AlertTitle>
                  <AlertDescription>
                    Store your first aid kit in an easily accessible location and check expiration dates regularly. 
                    Consider taking a pet first aid course to be better prepared.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="emergency" className="mt-8">
            <div className="space-y-6">
              {emergencyProcedures.map((procedure, index) => (
                <motion.div
                  key={procedure.emergency}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-xl text-red-600 flex items-center">
                        <AlertTriangle className="w-5 h-5 mr-2" />
                        Emergency: {procedure.emergency}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                          <h4 className="font-semibold text-gray-700 mb-3">Emergency Steps:</h4>
                          <ol className="space-y-2">
                            {procedure.steps.map((step, idx) => (
                              <li key={idx} className="flex items-start">
                                <span className="bg-red-500 text-white text-sm rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                                  {idx + 1}
                                </span>
                                <span className="text-gray-700">{step}</span>
                              </li>
                            ))}
                          </ol>
                        </div>
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-red-700 mb-2 flex items-center">
                            <X className="w-4 h-4 mr-1" />
                            Critical Warning:
                          </h4>
                          <p className="text-sm text-red-600">{procedure.warning}</p>
                          <div className="mt-3 p-2 bg-red-100 rounded">
                            <p className="text-xs text-red-700 font-medium">
                              Always seek immediate veterinary care for emergencies
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Emergency Contacts */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-red-600">Emergency Contacts</CardTitle>
            <CardDescription className="text-center">
              Keep these numbers easily accessible
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <Phone className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <h3 className="font-semibold text-red-800">Your Veterinarian</h3>
                <p className="text-sm text-red-600">Regular vet clinic number</p>
              </div>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <Clock className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <h3 className="font-semibold text-red-800">24-Hour Emergency Clinic</h3>
                <p className="text-sm text-red-600">Nearest emergency animal hospital</p>
              </div>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertTriangle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <h3 className="font-semibold text-red-800">Pet Poison Helpline</h3>
                <p className="text-sm text-red-600">(*************</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
